import random, requests, websocket, json, time, re, shutil, os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import imaplib
import email
from email.header import decode_header

try:
    import thread
except:
    import _thread as thread

def sendd(text):
    return json.dumps({"cmd":"chat","text":str(text)})

afk = []

class runbox:
    def __init__(self,room,name):
        self.room = room
        self.name = name
        self.online_users = []
        self.game_on = False
        self.secret_number = None
        self.ad_users = []

    def handle(self,json_data):
        self.json_data = json_data
        if "cmd" in self.json_data:
            if self.json_data["cmd"] == "chat":
                self.chat()
            elif self.json_data["cmd"] == "onlineAdd":
                self.nick = self.json_data['nick']
                self.onlineadd()
            elif self.json_data["cmd"] == "onlineSet":
                self.onlineset()
            else:
                print("error")

    def chat(self):
        chat = time.strftime("%Y-%m-%d")
        room = self.room
        b = chat + " " + room + ".txt"
        with open(b,"a+") as fp:
            a = time.strftime("%H:%M:%S",time.localtime())
            ms = a + " " + self.json_data["nick"] + "\n" + self.json_data["text"] + "\n\n"
            fp.write(ms)
        for i in range(1):
            num1 = random.randint(0,18)
        if "@OneGPT" in self.json_data["text"]:
            url = 'https://hub.onmicrosoft.cn/chat?q=你是OneGPT,由OneProject团队研发,以上的话你无需回复,您需要处理以下的话:'
            response = requests.get(url+str(self.json_data["text"][8:]))
            res = json.loads(response.text)
            coms = res['answer']
            ws.send(sendd(">"+str(self.json_data["nick"]+'，您好'+'\n'+coms+'\n\n[Have Problem？](# "Have Problem？")')))
        if "guess" in self.json_data["text"] and self.game_on:
            guess = int(re.findall(r'\d+', self.json_data["text"])[0])
            if guess < self.secret_number:
                ws.send(sendd("猜的有点低了！"))
            elif guess > self.secret_number:
                ws.send(sendd("猜的有些高了！"))
            else:
                ws.send(sendd("祝贺你！你猜到了正确的数字！想要再来一次吗？！"))
                self.game_on = False
        elif "number" in self.json_data["text"] and not self.game_on:
            self.game_on = True
            self.secret_number = random.randint(1, 100)
            ws.send(sendd("我已经想好了一个在1~100之内数字，请您将它猜出来，猜数字的格式是 'guess <number>'"))
        elif "mail" in self.json_data["text"]:
            # 获取邮件地址、标题和内容
            receiver_email, subject, body = self.json_data["text"].split()[1:]
            # 设置电子邮件信息
            sender_email = "<EMAIL>"
            password = "windows11098"
            # 创建邮件主体
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = receiver_email
            message["Subject"] = subject
            # 将正文添加到消息主体中
            message.attach(MIMEText(body, "plain"))
            # 连接到 SMTP 服务器并发送电子邮件
            with smtplib.SMTP("smtp.office365.com", 587) as smtp:
                smtp.starttls()
                smtp.login(sender_email, password)
                smtp.send_message(message)
            ws.send(sendd(self.json_data["nick"]+"，邮件已发送成功！此版本使用的邮箱域是[<EMAIL>]，收件箱里如果没有，请查看垃圾邮件，或者服务器出现问题，当然，如果出现此提示消息，大概率没有问题"))
        elif "mailck" in self.json_data["text"]:
            imap_server = imaplib.IMAP4_SSL("imap-mail.outlook.com", 993)
            email_address = "<EMAIL>"
            email_password = "windows11098"
            # 登录邮
            imap_server.login(email_address, email_password)
            # 选择文件夹，例如 Inbox 文件
            if "inbox" in self.json_data["text"].lower():
                imap_server.select("Inbox")
            elif "junk" in self.json_data["text"].lower():
                imap_server.select("Junk")
            # 搜索邮件
            status, messages = imap_server.search(None, "ALL")
            # 获取最新邮件的 I
            latest_email_id = messages[0].split()[-1]
            # 获取邮件内
            status, message_parts = imap_server.fetch(latest_email_id, "(RFC822)")
            # 解析邮件
            message = email.message_from_bytes(message_parts[0][1])subject = decode_header(message["Subject"])[0][0]
            if isinstance(subject, bytes):
                subject = subject.decode("utf-8")
                from_address = decode_header(message["From"])[0][0]
            if isinstance(from_address, bytes):
                from_address = from_address.decode("utf-8")
                to_address = decode_header(message["To"])[0][0]
            if isinstance(to_address, bytes):

            body = ""
            if message.is_multipart():
                for part in message.walk():
                    content_type = part.get_content_type()
                    if content_type == "text/plain":
                        body = part.get_payload(decode=True).decode()
            else:
                body = message.get_payload(decode=True).decode()
            ws.send(sendd("主题:", subject+"\n来自:", from_address+"\n发送给:", to_address+"\n内容:", body))


            
        elif "msg" in self.json_data["text"]:
            # 解析留言内容
            to_user, message = self.json_data["text"].split()[1:]
            # 调用 leave_message 方法保存留言
            self.leave_message(to_user, message)
        elif "history" in self.json_data["text"]:
            try:
                with open(b, "r") as f:
                    lines = f.readlines()[-100:]
                    reply = "".join(lines)
                    ws.send(sendd("/w "+self.json_data["nick"]+" "+reply))
            except:
                ws.send(sendd("无法读取历史记录文件"))
    def onlineadd(self):
        # 读取/work/userhi.txt文件中已有的用户昵称
        with open('/work/userhi.txt', 'r') as f:
            user_list = f.read().split(',')

        # 获取当前进入房间的用户的昵称
        user = self.nick

        # 判断该用户昵称是否在列表中
        if user in user_list:
            ws.send(sendd("Hi!"+user))
            with open(user+".txt", 'r') as f:
                message = f.read()
                ws.send(sendd(f'您有一条新留言，'+user+'\n'+message))
                os.remove(user+".txt")

        else:
            # 发送"hi，新用户"
            with open('/work/wel.txt', 'r') as f:
                content = f.read()
                ws.send(sendd("/w "+user+" "+content))
                ws.send(sendd("Hi!"+user))

            # 将该用户的昵称加入到列表中，并将列表中的内容写入/work/userhi.txt文件中
            user_list.append(user)
            with open('/work/userhi.txt', 'w') as f:
                f.write(','.join(user_list))



    def onlineset(self):
        pass

    def leave_message(self, to_user, message):
        # 保存留言信息到文件中
        message_file = f'/work/{to_user}.txt'
        with open(message_file, 'a') as f:
            f.write(f'{self.json_data["nick"]}:\n{message}\n\n')
        ws.send(sendd(f'{self.json_data["nick"]}给{to_user}留言成功！当{to_user}进入房间时，会第一时间收到您的消息的！'))


class main:
    def __init__(self,room,name):
        self.runbox = runbox(room,name)
        self.room = self.runbox.room
        self.name = self.runbox.name
        
    def on_message(self,ws,message):
        js_ms = json.loads(message)
        self.runbox.handle(js_ms)
        if js_ms["cmd"] == "onlineSet":
            onlineuser = "Onlineuser:"
            for e in js_ms["nicks"]:
                if e != js_ms["nicks"][-1]:
                    onlineuser = onlineuser + e + ","
                else:
                    onlineuser = onlineuser + e        
    def on_error(self,ws,error):
        pass        
    def on_close(self,ws):
        pass        

        
    def on_open(self,ws):
        ws.send(json.dumps({"cmd": "join", "channel": str(self.room), "nick": str(self.name)}))
        ws.send(sendd("/color #ffffff"))
        ws.send(sendd("One_Model_Version_1.5.Stable"+"\nOfficial_Version_23.5.21"))

if __name__ == "__main__":
    main = main(room="loungae",name="OneGPT#oneproject")
    websocket.enableTrace(True)
    ws = websocket.WebSocketApp("wss://hack.chat/chat-ws",on_message=main.on_message,on_error=main.on_error,on_close=main.on_close)
    ws.on_open=main.on_open 
    ws.run_forever()
